Create a Logtalk project.

The Logtalk distribution includes several sample files to help you get started in a new project by copying them to your project folder and removing the `-sample` text from their names:

- `settings-sample.lgt`
- `loader-sample.lgt`
- `tester-sample.lgt`
- `tests-sample.lgt`

The `coding` directory also contains an EditorConfig file and "ignore" files for several source code management systems such as git.

The "Logtalk: Create Project" command can be used to create a new project with copies of the sample files already renamed and ready to be customized.
