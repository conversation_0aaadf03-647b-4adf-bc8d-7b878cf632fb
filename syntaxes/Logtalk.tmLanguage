<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>fileTypes</key>
	<array>
		<string>lgt</string>
		<string>logtalk</string>
	</array>
	<key>keyEquivalent</key>
	<string>^~L</string>
	<key>name</key>
	<string>Logtalk</string>
	<key>patterns</key>
	<array>
		<dict>
			<key>begin</key>
			<string>/\*</string>
			<key>captures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.logtalk</string>
				</dict>
			</dict>
			<key>end</key>
			<string>\*/</string>
			<key>name</key>
			<string>comment.block.logtalk</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.comment.logtalk</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(%).*$\n?</string>
			<key>name</key>
			<string>comment.line.percentage.logtalk</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>storage.type.opening.logtalk</string>
				</dict>
				<key>2</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.logtalk</string>
				</dict>
				<key>4</key>
				<dict>
					<key>name</key>
					<string>entity.name.type.logtalk</string>
				</dict>
			</dict>
			<key>match</key>
			<string>((:-)\s(object|protocol|category|module))(?:\()([^(,)]+)</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.type.logtalk</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(:-)\s(end_(object|protocol|category))(?=[.])</string>
			<key>name</key>
			<string>storage.type.closing.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(complements|extends|i(nstantiates|mp(orts|lements))|specializes)(?=[(])</string>
			<key>name</key>
			<string>storage.type.relations.logtalk</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.modifier.logtalk</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(:-)\s(e(lse|ndif)|built_in|dynamic|synchronized|threaded)(?=[.])</string>
			<key>name</key>
			<string>storage.modifier.others.logtalk</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.modifier.logtalk</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(:-)\s(c(alls|oinductive)|e(lif|n(coding|sure_loaded)|xport)|i(f|n(clude|itialization|fo))|reexport|set_(logtalk|prolog)_flag|uses)(?=[(])</string>
			<key>name</key>
			<string>storage.modifier.others.logtalk</string>
		</dict>
		<dict>
			<key>captures</key>
			<dict>
				<key>1</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.storage.modifier.logtalk</string>
				</dict>
			</dict>
			<key>match</key>
			<string>(:-)\s(alias|info|d(ynamic|iscontiguous)|m(eta_(non_terminal|predicate)|ode|ultifile)|p(ublic|r(otected|ivate))|op|use(s|_module)|synchronized)(?=[(])</string>
			<key>name</key>
			<string>storage.modifier.others.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(:-)</string>
			<key>name</key>
			<string>keyword.operator.clause.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(::|:|\^\^)</string>
			<key>name</key>
			<string>keyword.operator.message-sending.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>([{}])</string>
			<key>name</key>
			<string>keyword.operator.external-call.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(@=&lt;|@&lt;|@&gt;=|@&gt;|==|\\==)</string>
			<key>name</key>
			<string>keyword.operator.comparison.term.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(\?|@)</string>
			<key>name</key>
			<string>keyword.operator.mode.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>([!]|\\\+|,|;|--&gt;|-&gt;|=\.\.|=|\\=|\.|\^|\b(as|is)\b)</string>
			<key>name</key>
			<string>keyword.operator.misc.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(=&lt;|&lt;|&gt;=|&gt;|=:=|=\\=)</string>
			<key>name</key>
			<string>keyword.operator.comparison.arithmetic.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(&lt;&lt;|&gt;&gt;|/\\|\\/|\\)</string>
			<key>name</key>
			<string>keyword.operator.bitwise.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(e|pi|mod|rem|div)\b(?![-!(^~])</string>
			<key>name</key>
			<string>support.function.evaluable.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>(\*\*|\+|-|\*|/|//)</string>
			<key>name</key>
			<string>support.function.evaluable.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(a(bs|cos|sin|tan|tan2)|c(eiling|os)|div|exp|flo(at(_(integer|fractional)_part)?|or)|log|m(ax|in|od)|r(em|ound)|s(i(n|gn)|qrt)|t(an|runcate)|xor)(?=[(])</string>
			<key>name</key>
			<string>support.function.evaluable.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(true|fa(il|lse)|repeat|(instantiation|system)_error)\b(?![-!(^~])</string>
			<key>name</key>
			<string>support.function.control.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(ca(ll|tch)|ignore|throw|once)(?=[(])</string>
			<key>name</key>
			<string>support.function.control.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((type|domain|consistency|existence|permission|representation|evaluation|resource|syntax|uninstantiation)_error)(?=[(])</string>
			<key>name</key>
			<string>support.function.control.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((get|p(eek|ut))_(c(har|ode)|byte)|nl)(?=[(])</string>
			<key>name</key>
			<string>support.function.chars-and-bytes-io.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\bnl\b</string>
			<key>name</key>
			<string>support.function.chars-and-bytes-io.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(atom_(length|c(hars|o(ncat|des)))|sub_atom|char_code|number_c(har|ode)s)(?=[(])</string>
			<key>name</key>
			<string>support.function.atom-term-processing.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(var|atom(ic)?|integer|float|c(allable|ompound)|n(onvar|umber)|ground|acyclic_term)(?=[(])</string>
			<key>name</key>
			<string>support.function.term-testing.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(compare)(?=[(])</string>
			<key>name</key>
			<string>support.function.term-comparison.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(read(_term)?|write(q|_(canonical|term))?|(current_)?(char_conversion|op))(?=[(])</string>
			<key>name</key>
			<string>support.function.term-io.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(arg|copy_term|functor|numbervars|term_variables)(?=[(])</string>
			<key>name</key>
			<string>support.function.term-creation-and-decomposition.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(subsumes_term|unify_with_occurs_check)(?=[(])</string>
			<key>name</key>
			<string>support.function.term-unification.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((se|curren)t_(in|out)put|open|close|flush_output|stream_property|at_end_of_stream|set_stream_position)(?=[(])</string>
			<key>name</key>
			<string>support.function.stream-selection-and-control.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(flush_output|at_end_of_stream)\b</string>
			<key>name</key>
			<string>support.function.stream-selection-and-control.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((se|curren)t_prolog_flag)(?=[(])</string>
			<key>name</key>
			<string>support.function.prolog-flags.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(logtalk_(compile|l(ibrary_path|oad|oad_context)|make(_target_action)?))(?=[(])</string>
			<key>name</key>
			<string>support.function.compiling-and-loading.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(logtalk_make)\b</string>
			<key>name</key>
			<string>support.function.compiling-and-loading.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((abolish|define)_events|current_event)(?=[(])</string>
			<key>name</key>
			<string>support.function.event-handling.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((create|current|set)_logtalk_flag|halt)(?=[(])</string>
			<key>name</key>
			<string>support.function.implementation-defined-hooks.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(halt)\b</string>
			<key>name</key>
			<string>support.function.implementation-defined-hooks.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((key)?(sort))(?=[(])</string>
			<key>name</key>
			<string>support.function.sorting.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((c(reate|urrent)|abolish)_(object|protocol|category))(?=[(])</string>
			<key>name</key>
			<string>support.function.entity-creation-and-abolishing.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((object|protocol|category)_property|co(mplements_object|nforms_to_protocol)|extends_(object|protocol|category)|imp(orts_category|lements_protocol)|(instantiat|specializ)es_class)(?=[(])</string>
			<key>name</key>
			<string>support.function.reflection.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((for|retract)all)(?=[(])</string>
			<key>name</key>
			<string>support.function.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(context|parameter|se(lf|nder)|this)(?=[(])</string>
			<key>name</key>
			<string>support.function.execution-context.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(a(bolish|ssert(a|z))|clause|retract(all)?)(?=[(])</string>
			<key>name</key>
			<string>support.function.database.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b((bag|set)of|f(ind|or)all)(?=[(])</string>
			<key>name</key>
			<string>support.function.all-solutions.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(threaded(_(ca(ll|ncel)|once|ignore|exit|peek|wait|notify))?)(?=[(])</string>
			<key>name</key>
			<string>support.function.multi-threading.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(threaded_engine(_(create|destroy|self|next|next_reified|yield|post|fetch))?)(?=[(])</string>
			<key>name</key>
			<string>support.function.engines.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(current_predicate|predicate_property)(?=[(])</string>
			<key>name</key>
			<string>support.function.reflection.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(before|after)(?=[(])</string>
			<key>name</key>
			<string>support.function.event-handler.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(forward)(?=[(])</string>
			<key>name</key>
			<string>support.function.message-forwarding-handler.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(expand_(goal|term)|(goal|term)_expansion|phrase)(?=[(])</string>
			<key>name</key>
			<string>support.function.grammar-rule.logtalk</string>
		</dict>
		<dict>
			<key>begin</key>
			<string>'</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.logtalk</string>
				</dict>
			</dict>
			<key>end</key>
			<string>'</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.logtalk</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.single.logtalk</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\([\\abfnrtv"']|(x[a-fA-F0-9]+|[0-7]+)\\)</string>
					<key>name</key>
					<string>constant.character.escape.logtalk</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>begin</key>
			<string>"</string>
			<key>beginCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.begin.logtalk</string>
				</dict>
			</dict>
			<key>end</key>
			<string>"</string>
			<key>endCaptures</key>
			<dict>
				<key>0</key>
				<dict>
					<key>name</key>
					<string>punctuation.definition.string.end.logtalk</string>
				</dict>
			</dict>
			<key>name</key>
			<string>string.quoted.double.logtalk</string>
			<key>patterns</key>
			<array>
				<dict>
					<key>match</key>
					<string>\\([\\abfnrtv"']|(x[a-fA-F0-9]+|[0-7]+)\\)</string>
					<key>name</key>
					<string>constant.character.escape.logtalk</string>
				</dict>
			</array>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(0b[0-1]+|0o[0-7]+|0x\h+)\b</string>
			<key>name</key>
			<string>constant.numeric.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(0'[\\]?.|0''|0'")</string>
			<key>name</key>
			<string>constant.numeric.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b(\d+\.?\d*((e|E)(\+|-)?\d+)?)\b</string>
			<key>name</key>
			<string>constant.numeric.logtalk</string>
		</dict>
		<dict>
			<key>match</key>
			<string>\b([A-Z_][A-Za-z0-9_]*)\b</string>
			<key>name</key>
			<string>variable.other.logtalk</string>
		</dict>
	</array>
	<key>scopeName</key>
	<string>source.logtalk</string>
	<key>uuid</key>
	<string>C11FA1F2-6EDB-11D9-8798-000A95DAA580</string>
</dict>
</plist>
