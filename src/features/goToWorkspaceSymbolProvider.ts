"use strict";

import {
  CancellationToken,
  WorkspaceSymbolProvider,
  Location,
  SymbolInformation,
  SymbolKind,
  workspace
} from "vscode";
import { getLogger } from "../utils/logger";
import { SymbolRegexes, SymbolTypes, SymbolUtils, PatternSets } from "../utils/symbols";

export class LogtalkWorkspaceSymbolProvider implements WorkspaceSymbolProvider {
  private logger = getLogger();

  public async provideWorkspaceSymbols(
    query: string,
    token: CancellationToken
  ): Promise<SymbolInformation[]> {
    const symbols: SymbolInformation[] = [];

    const docs = await workspace.findFiles('**/*.{lgt,logtalk}');
    for (let i = 0; i < docs.length; i++) {
      try {
        const doc = await workspace.openTextDocument(docs[i]);

        // Track predicates and non-terminals to only add first clause per file
        const seenPredicates = new Set<string>();
        const seenNonTerminals = new Set<string>();
        let insideTerm = false;

        for (let j = 0; j < doc.lineCount; j++) {
          const line = doc.lineAt(j);
          const lineText = line.text;

          // Check for entity opening directives
          const entityMatch = SymbolUtils.matchFirst(lineText, PatternSets.entityOpening);
          if (entityMatch) {
            const symbolKind = entityMatch.type === SymbolTypes.OBJECT ? SymbolKind.Class :
                              entityMatch.type === SymbolTypes.PROTOCOL ? SymbolKind.Interface :
                              SymbolKind.Struct;
            symbols.push(new SymbolInformation(
              entityMatch.match[1],
              symbolKind,
              entityMatch.type,
              new Location(doc.uri, line.range)
            ));
            continue;
          }

          // Check for scope directives
          const scopeMatch = SymbolUtils.matchFirst(lineText, PatternSets.allScopes);
          if (scopeMatch) {
            const symbolKind = scopeMatch.type.includes('non-terminal') ? SymbolKind.Field : SymbolKind.Function;
            symbols.push(new SymbolInformation(
              scopeMatch.match[1],
              symbolKind,
              scopeMatch.type,
              new Location(doc.uri, line.range)
            ));
            continue;
          }

          // Check for predicate clauses (only if not inside a multi-line term)
          if (!insideTerm) {
            const predicateMatch = lineText.match(SymbolRegexes.predicateClause);
            if (predicateMatch) {
              const predicateName = SymbolUtils.extractPredicateName(predicateMatch[1]);
              if (predicateName && !seenPredicates.has(predicateName)) {
                seenPredicates.add(predicateName);
                symbols.push(new SymbolInformation(
                  predicateMatch[1],
                  SymbolKind.Property,
                  SymbolTypes.PREDICATE_CLAUSE,
                  new Location(doc.uri, line.range)
                ));
              }
              continue;
            }

            // Check for non-terminal rules (only if not inside a multi-line term)
            const nonTerminalMatch = lineText.match(SymbolRegexes.nonTerminalRule);
            if (nonTerminalMatch) {
              const nonTerminalName = SymbolUtils.extractNonTerminalName(nonTerminalMatch[1]);
              if (nonTerminalName && !seenNonTerminals.has(nonTerminalName)) {
                seenNonTerminals.add(nonTerminalName);
                symbols.push(new SymbolInformation(
                  nonTerminalMatch[1],
                  SymbolKind.Property,
                  SymbolTypes.NON_TERMINAL_RULE,
                  new Location(doc.uri, line.range)
                ));
              }
              continue;
            }
          }

          // Update inside_term state based on line ending
          insideTerm = SymbolUtils.isContinuationLine(lineText);
        }
      } catch(err) {
        this.logger.debug("failed to open " + docs[i]);
      }
    }

    return symbols;
  }
}
