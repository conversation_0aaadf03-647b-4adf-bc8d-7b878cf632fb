"use strict";

import {
  CancellationToken,
  WorkspaceSymbolProvider,
  Location,
  SymbolInformation,
  SymbolKind,
  workspace
} from "vscode";
import { getLogger } from "../utils/logger";
import { SymbolRegexes, SymbolTypes, SymbolUtils, PatternSets } from "../utils/symbols";

export class LogtalkWorkspaceSymbolProvider implements WorkspaceSymbolProvider {
  private logger = getLogger();

  public async provideWorkspaceSymbols(
    query: string,
    token: CancellationToken
  ): Promise<SymbolInformation[]> {
    const symbols: SymbolInformation[] = [];

    const docs = await workspace.findFiles('**/*.{lgt,logtalk}');
    for (let i = 0; i < docs.length; i++) {
      try {
        const doc = await workspace.openTextDocument(docs[i]);

        // Track current entity and predicates/non-terminals per entity
        let currentEntity: string | null = null;
        let currentEntityType: string | null = null;
        const entityPredicates = new Map<string, Set<string>>(); // entity -> set of predicate names
        const entityNonTerminals = new Map<string, Set<string>>(); // entity -> set of non-terminal names
        let insideTerm = false;

        for (let j = 0; j < doc.lineCount; j++) {
          const line = doc.lineAt(j);
          const lineText = line.text;

          // Check for entity opening directives
          const entityMatch = SymbolUtils.matchFirst(lineText, PatternSets.entityOpening);
          if (entityMatch) {
            currentEntity = entityMatch.match[1];
            currentEntityType = entityMatch.type;
            entityPredicates.set(currentEntity, new Set<string>());
            entityNonTerminals.set(currentEntity, new Set<string>());

            const symbolKind = entityMatch.type === SymbolTypes.OBJECT ? SymbolKind.Class :
                              entityMatch.type === SymbolTypes.PROTOCOL ? SymbolKind.Interface :
                              SymbolKind.Struct;
            symbols.push(new SymbolInformation(
              entityMatch.match[1],
              symbolKind,
              entityMatch.type,
              new Location(doc.uri, line.range)
            ));
            continue;
          }

          // Check for entity ending directives
          const entityEndMatch = SymbolUtils.matchFirst(lineText, PatternSets.entityEnding);
          if (entityEndMatch) {
            currentEntity = null;
            currentEntityType = null;
            continue;
          }

          // Check for scope directives
          const scopeMatch = SymbolUtils.matchFirst(lineText, PatternSets.allScopes);
          if (scopeMatch) {
            const symbolKind = scopeMatch.type.includes('non-terminal') ? SymbolKind.Field : SymbolKind.Function;
            const containerName = currentEntity ? `${scopeMatch.type} • ${currentEntity} (${currentEntityType})` : scopeMatch.type;
            symbols.push(new SymbolInformation(
              scopeMatch.match[1],
              symbolKind,
              containerName,
              new Location(doc.uri, line.range)
            ));
            continue;
          }

          // Check for predicate clauses (only if not inside a multi-line term and inside an entity)
          if (!insideTerm && currentEntity) {
            const predicateMatch = lineText.match(SymbolRegexes.predicateClause);
            if (predicateMatch) {
              const predicateName = SymbolUtils.extractPredicateName(predicateMatch[1]);
              if (predicateName) {
                const entityPredicateSet = entityPredicates.get(currentEntity);
                if (entityPredicateSet && !entityPredicateSet.has(predicateName)) {
                  entityPredicateSet.add(predicateName);
                  const containerName = `${SymbolTypes.PREDICATE_CLAUSE} • ${currentEntity} (${currentEntityType})`;
                  symbols.push(new SymbolInformation(
                    predicateMatch[1],
                    SymbolKind.Property,
                    containerName,
                    new Location(doc.uri, line.range)
                  ));
                }
              }
              continue;
            }

            // Check for non-terminal rules (only if not inside a multi-line term and inside an entity)
            const nonTerminalMatch = lineText.match(SymbolRegexes.nonTerminalRule);
            if (nonTerminalMatch) {
              const nonTerminalName = SymbolUtils.extractNonTerminalName(nonTerminalMatch[1]);
              if (nonTerminalName) {
                const entityNonTerminalSet = entityNonTerminals.get(currentEntity);
                if (entityNonTerminalSet && !entityNonTerminalSet.has(nonTerminalName)) {
                  entityNonTerminalSet.add(nonTerminalName);
                  const containerName = `${SymbolTypes.NON_TERMINAL_RULE} • ${currentEntity} (${currentEntityType})`;
                  symbols.push(new SymbolInformation(
                    nonTerminalMatch[1],
                    SymbolKind.Property,
                    containerName,
                    new Location(doc.uri, line.range)
                  ));
                }
              }
              continue;
            }
          }

          // Update inside_term state based on line ending
          insideTerm = SymbolUtils.isContinuationLine(lineText);
        }
      } catch(err) {
        this.logger.debug("failed to open " + docs[i]);
      }
    }

    return symbols;
  }
}
