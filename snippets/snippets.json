{"directive:object": {"prefix": ":- obj", "body": ":- object(${1:name}).\n\t$2\n:- end_object.\n", "scope": "source.logtalk"}, "directive:category": {"prefix": ":- cat", "body": ":- category(${1:name}).\n\t$2\n:- end_category.\n", "scope": "source.logtalk"}, "directive:protocol": {"prefix": ":- pro", "body": ":- protocol(${1:name}).\n\t$2\n:- end_protocol.\n", "scope": "source.logtalk"}, "directive:objrelation": {"prefix": "orel", "description": "relations between objects", "body": "${1|imports,implements,extends,instantiates,specializes|}($2)"}, "directive:catrelation": {"prefix": "crel", "description": "relations between categories", "body": "${1|implements,extends,complements|}($2)"}, "directive:prorelation": {"prefix": "ext", "description": "relations between categories", "body": "extends(${1})"}, "category": {"prefix": "category", "body": "\n:- category(${1:Category},\n\timplements(${2:Protocol})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_category.\n", "description": "Category with protocol", "scope": "source.logtalk"}, "category1": {"prefix": "category", "body": "\n:- category(${1:Category}).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${2:Author}',\n\t\tdate is ${3:CURRENT_YEAR}-${4:CURRENT_MONTH}-${5:CURRENT_DATE},\n\t\tcomment is '${6:Description}'\n\t]).\n\n$0\n\n:- end_category.\n", "description": "Category", "scope": "source.logtalk"}, "class": {"prefix": "class", "body": "\n:- object(${1:Class},\n\timplements(${2:Protocol}),\n\timports(${3:Category}),\n\tinstantiates(${4:Metaclass}),\n\tspecializes(${5:Superclass})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${6:Author}',\n\t\tdate is ${7:CURRENT_YEAR}-${8:CURRENT_MONTH}-${9:CURRENT_DATE},\n\t\tcomment is '${10:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Class with all", "scope": "source.logtalk"}, "class1": {"prefix": "class", "body": "\n:- object(${1:Class},\n\timports(${2:Category}),\n\tspecializes(${3:Superclass})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${4:Author}',\n\t\tdate is ${5:CURRENT_YEAR}-${6:CURRENT_MONTH}-${7:CURRENT_DATE},\n\t\tcomment is '${8:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Class with category", "scope": "source.logtalk"}, "class2": {"prefix": "class", "body": "\n:- object(${1:Class},\n\tinstantiates(${2:Metaclass}),\n\tspecializes(${3:Superclass})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${4:Author}',\n\t\tdate is ${5:CURRENT_YEAR}-${6:CURRENT_MONTH}-${7:CURRENT_DATE},\n\t\tcomment is '${8:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Class with metaclass", "scope": "source.logtalk"}, "class3": {"prefix": "class", "body": "\n:- object(${1:Class},\n\timplements(${2:Protocol}),\n\tspecializes(${3:Superclass})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${4:Author}',\n\t\tdate is ${5:CURRENT_YEAR}-${6:CURRENT_MONTH}-${7:CURRENT_DATE},\n\t\tcomment is '${8:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Class with protocol", "scope": "source.logtalk"}, "class4": {"prefix": "class", "body": "\n:- object(${1:Class},\n\tspecializes(${2:Superclass})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Class", "scope": "source.logtalk"}, "category2": {"prefix": "category", "body": "\n:- category(${1:Category},\n\tcomplements(${2:Object})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_category.\n", "description": "Complementing category", "scope": "source.logtalk"}, "category3": {"prefix": "category", "body": "\n:- category(${1:ExtendedCategory},\n\textends(${2:MinimalCategory})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_category.\n", "description": "Extended category", "scope": "source.logtalk"}, "protocol": {"prefix": "protocol", "body": "\n:- protocol(${1:Extended},\n\textends(${2:Minimal})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_protocol.\n", "description": "Extended protocol", "scope": "source.logtalk"}, "instance": {"prefix": "instance", "body": "\n:- object(${1:Instance},\n\timplements(${2:Protocol}),\n\timports(${3:Category}),\n\tinstantiates(${4:Class})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${5:Author}',\n\t\tdate is ${6:CURRENT_YEAR}-${7:CURRENT_MONTH}-${8:CURRENT_DATE},\n\t\tcomment is '${9:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Instance with all", "scope": "source.logtalk"}, "instance1": {"prefix": "instance", "body": "\n:- object(${1:Instance},\n\timports(${2:Category}),\n\tinstantiates(${3:Class})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${4:Author}',\n\t\tdate is ${5:CURRENT_YEAR}-${6:CURRENT_MONTH}-${7:CURRENT_DATE},\n\t\tcomment is '${8:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Instance with category", "scope": "source.logtalk"}, "instance2": {"prefix": "instance", "body": "\n:- object(${1:Instance},\n\timplements(${2:Protocol}),\n\tinstantiates(${3:Class})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${4:Author}',\n\t\tdate is ${5:CURRENT_YEAR}-${6:CURRENT_MONTH}-${7:CURRENT_DATE},\n\t\tcomment is '${8:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Instance with protocol", "scope": "source.logtalk"}, "instance3": {"prefix": "instance", "body": "\n:- object(${1:Instance},\n\tinstantiates(${2:Class})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Instance", "scope": "source.logtalk"}, "private": {"prefix": "private", "body": "\t:- private(${1:Functor}/0).\n\t:- mode(${1:Functor}, ${2:Solutions}).\n\t:- info(${1:Functor}/0, [\n\t\tcomment is '${3:Description}'\n\t]).\n\n$0", "description": "(with no arguments)", "scope": "source.logtalk"}, "private1": {"prefix": "private", "body": "\t:- private(${1:Functor}/${2:<PERSON><PERSON>}).\n\t:- mode(${1:Functor}(${3:Arguments}), ${4:Solutions}).\n\t:- info(${1:Functor}/${2:<PERSON><PERSON>}, [\n\t\tcomment is '${5:Description}',\n\t\targuments is ['$6'-'$7']\n\t]).\n\n$0", "description": "Private predicate", "scope": "source.logtalk"}, "protected": {"prefix": "protected", "body": "\t:- protected(${1:Functor}/0).\n\t:- mode(${1:Functor}, ${2:Solutions}).\n\t:- info(${1:Functor}/0, [\n\t\tcomment is '${3:Description}'\n\t]).\n\n$0", "description": "(with no arguments)", "scope": "source.logtalk"}, "protected1": {"prefix": "protected", "body": "\t:- protected(${1:Functor}/${2:<PERSON><PERSON>}).\n\t:- mode(${1:Functor}(${3:Arguments}), ${4:Solutions}).\n\t:- info(${1:Functor}/${2:<PERSON><PERSON>}, [\n\t\tcomment is '${5:Description}',\n\t\targuments is ['$6'-'$7']\n\t]).\n\n$0", "description": "Protected predicate", "scope": "source.logtalk"}, "protocol1": {"prefix": "protocol", "body": "\n:- protocol(${1:Protocol}).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${2:Author}',\n\t\tdate is ${3:CURRENT_YEAR}-${4:CURRENT_MONTH}-${5:CURRENT_DATE},\n\t\tcomment is '${6:Description}'\n\t]).\n\n$0\n\n:- end_protocol.\n", "description": "Protocol", "scope": "source.logtalk"}, "object": {"prefix": "object", "body": "\n:- object(${1:Prototype},\n\timplements(${2:Protocol}),\n\timports(${3:Category}),\n\textends(${4:Parent})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${5:Author}',\n\t\tdate is ${6:CURRENT_YEAR}-${7:CURRENT_MONTH}-${8:CURRENT_DATE},\n\t\tcomment is '${9:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Prototype with all", "scope": "source.logtalk"}, "object1": {"prefix": "object", "body": "\n:- object(${1:Prototype},\n\timports(${2:Category})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Prototype with category", "scope": "source.logtalk"}, "object2": {"prefix": "object", "body": "\n:- object(${1:Prototype},\n\textends(${2:Parent})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Prototype with parent", "scope": "source.logtalk"}, "object3": {"prefix": "object", "body": "\n:- object(${1:Prototype},\n\timplements(${2:Protocol})).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${3:Author}',\n\t\tdate is ${4:CURRENT_YEAR}-${5:CURRENT_MONTH}-${6:CURRENT_DATE},\n\t\tcomment is '${7:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Prototype with protocol", "scope": "source.logtalk"}, "object4": {"prefix": "object", "body": "\n:- object(${1:Object}).\n\n\t:- info([\n\t\tversion is 1:0:0,\n\t\tauthor is '${2:Author}',\n\t\tdate is ${3:CURRENT_YEAR}-${4:CURRENT_MONTH}-${5:CURRENT_DATE},\n\t\tcomment is '${6:Description}'\n\t]).\n\n$0\n\n:- end_object.\n", "description": "Prototype", "scope": "source.logtalk"}, "public": {"prefix": "public", "body": "\t:- public(${1:Functor}/0).\n\t:- mode(${1:Functor}, ${2:Solutions}).\n\t:- info(${1:Functor}/0, [\n\t\tcomment is '${3:Description}'\n\t]).\n\n$0", "description": "(with no arguments)", "scope": "source.logtalk"}, "public1": {"prefix": "public", "body": "\t:- public(${1:Functor}/${2:<PERSON><PERSON>}).\n\t:- mode(${1:Functor}(${3:Arguments}), ${4:Solutions}).\n\t:- info(${1:Functor}/${2:<PERSON><PERSON>}, [\n\t\tcomment is '${5:Description}',\n\t\targuments is ['$6'-'$7']\n\t]).\n\n$0", "description": "Public predicate", "scope": "source.logtalk"}}